from langchain_community.vectorstores import FAISS
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langchain_huggingface import HuggingFaceEmbeddings
from langchain_community.embeddings import LlamaCppEmbeddings
import re
# Cache for embedding model
_cached_embeddings = None

def chunk_diff(diff_text):
    chunk_size = 512
    chunk_overlap = 128
    pattern = re.compile(r'(def|function|class|public|private|protected)\s+[\w<>,\s\[\]]+\s+[\w_]+\s*\(.*?\)')
    method_starts = [m.start() for m in pattern.finditer(diff_text)] + [len(diff_text)]
    
    if len(method_starts) > 1:
        chunks = [diff_text[method_starts[i]:method_starts[i+1]] for i in range(len(method_starts)-1)]
        return RecursiveCharacterTextSplitter(
            chunk_size=chunk_size, 
            chunk_overlap=chunk_overlap
        ).create_documents(chunks)
    
    # If no methods found, use standard text chunking
    return RecursiveCharacterTextSplitter(
        chunk_size=chunk_size, 
        chunk_overlap=chunk_overlap
    ).create_documents([diff_text])


def load_embedding_model():
    """Load and cache the embedding model instance."""
    global _cached_embeddings
    if _cached_embeddings is not None:
        return _cached_embeddings

    embedding_model_path = os.getenv("EMBEDDING_MODEL", "./models/all-MiniLM-L6-v2-Q4_K_M.gguf")

    if not os.path.exists(embedding_model_path):
        print(f"Embedding model file not found at {embedding_model_path}")
        return None

    try:
        _cached_embeddings = LlamaCppEmbeddings(model_path=embedding_model_path)
        print(f"Loaded embedding model: {embedding_model_path}")
        return _cached_embeddings
    except Exception as e:
        print(f"Failed to load GGUF embedding model: {e}")
        return None


def build_vector_store(documents):
    if not documents:
        return None

    embeddings = load_embedding_model()
    if embeddings is None:
        print("No embedding model available. Vector store disabled.")
        return None

    try:
        return FAISS.from_documents(documents, embeddings)
    except Exception as e:
        print(f"Failed to build vector store: {e}")
        return None

def search_similar_code(vector_store, query, k=3):
    if vector_store is None:
        return []
    
    try:
        return vector_store.similarity_search(query, k=k)
    except Exception as e:
        print(f"Vector search failed: {e}")
        return []
