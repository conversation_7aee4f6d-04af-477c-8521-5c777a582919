import json
from typing import List, Dict, Any
from .base import BaseAgent


class SummaryAgent(BaseAgent):
    """Agent specialized in summarizing review results."""
    
    @property
    def system_prompt(self) -> str:
        return """You are a specialized code review agent as well as programming expert focused on synthesizing and summarizing review feedback. Your expertise includes:

- Analyzing code chunks from pull request changes and understanding their purpose
- Synthesizing feedback from multiple review agents
- Identifying the most critical issues and priorities
- Providing clear, actionable summaries
- Evaluating overall code quality and implementation approach
- Communicating technical feedback in accessible language

Guidelines:
- Provide natural language summaries, not JSON
- Start with the main purpose of the code changes
- Highlight critical issues first, then minor ones
- Be constructive and solution-oriented
- Consider the bigger picture and architectural implications
- Balance technical accuracy with readability
- Work with code chunks that may be semantically related parts of the changes

Your goal is to provide developers with clear, prioritized feedback they can act on."""

    def process(self, chunks: List[Any], feedbacks: List[Any]) -> Dict[str, Any]:
        """Process and summarize all review feedback."""
        # Extract meaningful content from feedbacks
        extracted_summaries = []
        for f in feedbacks:
            if f:
                if isinstance(f, dict):
                    # For agents that return {"lines": [...]} format
                    if "lines" in f and f["lines"]:
                        extracted_summaries.append(f)
                    # For agents that return {"summary": "..."} format
                    elif "summary" in f:
                        extracted_summaries.append(f["summary"])
                    else:
                        extracted_summaries.append(f)
                else:
                    extracted_summaries.append(str(f))

        # Extract content from chunks
        chunk_content = ""
        if chunks:
            chunk_content = "\n".join([
                chunk.page_content if hasattr(chunk, 'page_content') else str(chunk)
                for chunk in chunks
            ])

        # Debug logging
        print(f"SummaryAgent Debug:")
        print(f"- Number of chunks: {len(chunks) if chunks else 0}")
        print(f"- Chunk content length: {len(chunk_content)}")
        print(f"- Number of feedbacks: {len(feedbacks) if feedbacks else 0}")
        print(f"- Extracted summaries: {len(extracted_summaries)}")
        print(f"- Chunk content preview: {chunk_content[:200]}..." if chunk_content else "- No chunk content")
        print(f"- Feedbacks preview: {extracted_summaries[:2]}" if extracted_summaries else "- No feedbacks")

        user_prompt = f"""Analyze the following pull request code chunks and review feedbacks to provide a comprehensive summary.

TASK:
1. Identify what the PR is trying to accomplish based on the code changes
2. Evaluate the overall implementation quality
3. Summarize the key issues found by the review agents
4. Provide actionable recommendations

Be specific about the actual code changes and issues found. Avoid generic responses.

[CODE CHUNKS]
{chunk_content if chunk_content else "No code chunks available"}

[REVIEW FEEDBACKS]
Naming Issues: {json.dumps(feedbacks[0] if len(feedbacks) > 0 and feedbacks[0] else {}, indent=2)}
Syntax Issues: {json.dumps(feedbacks[1] if len(feedbacks) > 1 and feedbacks[1] else {}, indent=2)}
Logic Issues: {json.dumps(feedbacks[2] if len(feedbacks) > 2 and feedbacks[2] else {}, indent=2)}

Respond with a natural language paragraph that specifically addresses the code changes and issues found."""

        try:
            summary_text = self.invoke(user_prompt).strip()
            return {
                "summary": summary_text,
                "details": extracted_summaries
            }
        except Exception as e:
            return {
                "summary": f"Failed to summarize issues: {str(e)}",
                "details": extracted_summaries
            }
